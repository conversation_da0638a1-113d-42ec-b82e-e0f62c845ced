<?php
// DemandeController to handle all demande-related operations

class <PERSON>mandeControll<PERSON> extends Controller {
    private $demandeModel;
    private $userModel;
    private $leaveBalanceModel;
    private $departmentLeaveBalanceModel;

    public function __construct() {
        // Check if user is logged in
        Auth::requireLogin();

        // Load models
        $this->demandeModel = new DemandeModel();
        $this->userModel = new UserModel();
        $this->leaveBalanceModel = new LeaveBalanceModel();
        $this->departmentLeaveBalanceModel = new DepartmentLeaveBalanceModel();
    }

    // List all demandes for the logged-in user
    public function liste() {
        try {
            // Debug: Check the user ID in the session
            error_log("Current user ID in session: " . ($_SESSION['user_id'] ?? 'Not set'));

            // Check if this is an AJAX request
            $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest';

            // Get filters from URL parameters
            $period = $_GET['period'] ?? 'all';
            $status = $_GET['status'] ?? 'all';
            $type = $_GET['type'] ?? 'all';
            $search = $_GET['search'] ?? '';
            $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
            $perPage = isset($_GET['per_page']) ? max(5, min(50, intval($_GET['per_page']))) : 5;
            $sortBy = $_GET['sort_by'] ?? 'date_demande';
            $sortOrder = $_GET['sort_order'] ?? 'DESC';

            // Debug: Log the query parameters
            error_log("Query parameters: period=$period, status=$status, type=$type, search=$search, page=$page, perPage=$perPage, sortBy=$sortBy, sortOrder=$sortOrder");

            // Get user's demandes from model with filters, search, pagination and sorting
            $result = $this->demandeModel->getDemandesForUserWithFilters(
                $_SESSION['user_id'],
                $period,
                $status,
                $type,
                $search,
                $page,
                $perPage,
                $sortBy,
                $sortOrder
            );

            // Extract demandes and pagination info
            $demandes = $result['demandes'];
            $pagination = $result['pagination'];

            // Check if we have any results
            if ($demandes === false) {
                // Handle database error
                $demandes = [];
                $error = "Une erreur est survenue lors de la récupération des demandes.";
                $this->view('demandes/liste', [
                    'demandes' => $demandes,
                    'error' => $error,
                    'filters' => [
                        'period' => $period,
                        'status' => $status,
                        'type' => $type,
                        'search' => $search
                    ],
                    'pagination' => [
                        'total' => 0,
                        'perPage' => $perPage,
                        'currentPage' => $page,
                        'totalPages' => 0
                    ],
                    'sorting' => [
                        'sortBy' => $sortBy,
                        'sortOrder' => $sortOrder
                    ]
                ]);
                return;
            }

            // Check if success message or error passed via GET parameters
            $success = $_GET['success'] ?? null;
            $error = $_GET['error'] ?? null;

            // If this is an AJAX request, return JSON data
            if ($isAjax) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'demandes' => $demandes,
                    'pagination' => $pagination,
                    'filters' => [
                        'period' => $period,
                        'status' => $status,
                        'type' => $type,
                        'search' => $search
                    ],
                    'sorting' => [
                        'sortBy' => $sortBy,
                        'sortOrder' => $sortOrder
                    ]
                ]);
                return;
            }

            // Determine which view to use based on the URL
            $viewPath = 'demandes/liste';

            // Check if the request is coming from the planificateur route
            if (strpos($_SERVER['REQUEST_URI'], '/planificateur/mes-demandes') === 0) {
                $viewPath = 'planificateur/mes_demandes';
            }
            // Check if the request is coming from the responsable route
            elseif (strpos($_SERVER['REQUEST_URI'], '/responsable/mes-demandes') === 0) {
                $viewPath = 'responsable/mes_demandes';
            }

            $this->view($viewPath, [
                'demandes' => $demandes,
                'success' => $success,
                'error' => $error,
                'filters' => [
                    'period' => $period,
                    'status' => $status,
                    'type' => $type,
                    'search' => $search
                ],
                'pagination' => $pagination,
                'sorting' => [
                    'sortBy' => $sortBy,
                    'sortOrder' => $sortOrder
                ]
            ]);

        } catch (Exception $e) {
            // Determine which view to use based on the URL
            $viewPath = 'demandes/liste';

            // Check if the request is coming from the planificateur route
            if (strpos($_SERVER['REQUEST_URI'], '/planificateur/mes-demandes') === 0) {
                $viewPath = 'planificateur/mes_demandes';
            }
            // Check if the request is coming from the responsable route
            elseif (strpos($_SERVER['REQUEST_URI'], '/responsable/mes-demandes') === 0) {
                $viewPath = 'responsable/mes_demandes';
            }

            // Handle any unexpected exceptions
            $this->view($viewPath, [
                'demandes' => [],
                'error' => 'Une erreur système est survenue. Veuillez réessayer plus tard.',
                'filters' => [
                    'period' => 'all',
                    'status' => 'all',
                    'type' => 'all',
                    'search' => ''
                ],
                'pagination' => [
                    'total' => 0,
                    'perPage' => 5,
                    'currentPage' => 1,
                    'totalPages' => 0
                ],
                'sorting' => [
                    'sortBy' => 'date_demande',
                    'sortOrder' => 'DESC'
                ]
            ]);
        }
    }

    // Display form to create a new demande
    public function nouvelle() {
        // Get user's leave balances for display in the form
        $leaveBalances = $this->leaveBalanceModel->getUserLeaveBalances($_SESSION['user_id']);

        // Get next accrual date (first day of next month)
        $nextMonth = new DateTime('first day of next month');
        $nextAccrualDate = $nextMonth->format('Y-m-d');

        // Get user's role to determine accrual rate
        $user = $this->userModel->getUserById($_SESSION['user_id']);
        $accrualRate = 1.4; // Default for regular employees

        if ($user['role'] === 'responsable') {
            $accrualRate = 1.83;
        } elseif ($user['role'] === 'admin') {
            $accrualRate = 1.99;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Process form submission
            $type = $_POST['type'] ?? '';
            $dateDebut = $_POST['date_debut'] ?? '';
            $dateFin = $_POST['date_fin'] ?? '';
            $motif = $_POST['motif'] ?? '';

            // Debug logging to help identify the issue
            error_log("Form submission received - Raw values: Type: '" . var_export($type, true) . "', Date debut: '" . var_export($dateDebut, true) . "', Date fin: '" . var_export($dateFin, true) . "'");

            // Handle half-day options
            $demiJourDebut = isset($_POST['demi_jour_debut']) ? 1 : 0;
            $demiJourFin = isset($_POST['demi_jour_fin']) ? 1 : 0;
            $periodeDebut = $_POST['periode_debut'] ?? 'matin';
            $periodeFin = $_POST['periode_fin'] ?? 'apres-midi';

            // Determine demi_type value
            $demiType = null;
            if ($demiJourDebut && $demiJourFin) {
                $demiType = $periodeDebut . ',' . $periodeFin;
            } elseif ($demiJourDebut) {
                $demiType = $periodeDebut;
            } elseif ($demiJourFin) {
                $demiType = $periodeFin;
            }

            // Set demi_journee flag
            $demiJournee = ($demiJourDebut || $demiJourFin) ? 1 : 0;

            // Trim whitespace from input values
            $type = trim($type);
            $dateDebut = trim($dateDebut);
            $dateFin = trim($dateFin);
            $motif = trim($motif);

            // Validate input - check for empty or whitespace-only values
            $missingFields = [];
            if (empty($type)) {
                $missingFields[] = 'Type de demande';
            }
            if (empty($dateDebut)) {
                $missingFields[] = 'Date de début';
            }
            if (empty($dateFin)) {
                $missingFields[] = 'Date de fin';
            }

            if (!empty($missingFields)) {
                // Add debugging information to help identify the issue
                error_log("Form validation failed - Missing fields: " . implode(', ', $missingFields) . " - Type: '$type', Date debut: '$dateDebut', Date fin: '$dateFin'");

                $errorMessage = 'Les champs suivants sont obligatoires : ' . implode(', ', $missingFields);

                $this->view('demandes/nouvelle', [
                    'error' => $errorMessage,
                    'type' => $type,
                    'date_debut' => $dateDebut,
                    'date_fin' => $dateFin,
                    'motif' => $motif,
                    'demi_jour_debut' => $demiJourDebut,
                    'demi_jour_fin' => $demiJourFin,
                    'periode_debut' => $periodeDebut,
                    'periode_fin' => $periodeFin,
                    // Detailed leave balance information
                    'solde_paye' => $leaveBalances['payé']['remaining'],
                    'solde_paye_used' => $leaveBalances['payé']['used'],
                    'solde_paye_total' => $leaveBalances['payé']['total'],                    'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                    'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
                    'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

                    'solde_maladie' => $leaveBalances['maladie']['remaining'],
                    'solde_maladie_used' => $leaveBalances['maladie']['used'],
                    'solde_maladie_total' => $leaveBalances['maladie']['total'],

                    // Accrual information
                    'next_accrual_date' => $nextAccrualDate,
                    'accrual_rate' => $accrualRate
                ]);
                return;
            }

            // Validate dates
            try {
                // Try to validate and format dates
                if (!$this->isValidDate($dateDebut) || !$this->isValidDate($dateFin)) {
                    $this->view('demandes/nouvelle', [
                        'error' => 'Format de date invalide. Veuillez utiliser le format YYYY-MM-DD.',
                        'type' => $type,
                        'date_debut' => $dateDebut,
                        'date_fin' => $dateFin,
                        'motif' => $motif,
                        'solde_paye' => $leaveBalances['payé']['remaining'],

                        'solde_sans_solde' => $leaveBalances['sans solde']['remaining']
                    ]);
                    return;
                }

                $startDate = new DateTime($dateDebut);
                $endDate = new DateTime($dateFin);

                if ($endDate < $startDate) {
                    $this->view('demandes/nouvelle', [
                        'error' => 'La date de fin doit être postérieure ou égale à la date de début',
                        'type' => $type,
                        'date_debut' => $dateDebut,
                        'date_fin' => $dateFin,
                        'motif' => $motif,
                        'solde_paye' => $leaveBalances['payé']['remaining'],                        'solde_sans_solde' => $leaveBalances['sans solde']['remaining']
                    ]);
                    return;
                }

                // Validate that dates are not in the past
                $today = new DateTime();
                $today->setTime(0, 0, 0); // Reset time to start of day for fair comparison

                if ($startDate < $today) {
                    $this->view('demandes/nouvelle', [
                        'error' => 'La date de début ne peut pas être dans le passé',
                        'type' => $type,
                        'date_debut' => $dateDebut,
                        'date_fin' => $dateFin,
                        'motif' => $motif,
                        // Detailed leave balance information
                        'solde_paye' => $leaveBalances['payé']['remaining'],
                        'solde_paye_used' => $leaveBalances['payé']['used'],
                        'solde_paye_total' => $leaveBalances['payé']['total'],                        'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                        'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
                        'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

                        'solde_maladie' => $leaveBalances['maladie']['remaining'],
                        'solde_maladie_used' => $leaveBalances['maladie']['used'],
                        'solde_maladie_total' => $leaveBalances['maladie']['total'],                        // Accrual information
                        'next_accrual_date' => $nextAccrualDate,
                        'accrual_rate' => $accrualRate
                    ]);
                    return;
                }

                // Calculate the number of days for this request
                $requestDays = $this->demandeModel->calculateLeaveDays($dateDebut, $dateFin, $demiJournee);

                // VALIDATION RULE 1: For paid leave requests, require at least 7 days advance notice
                if ($type === 'payé') {
                    $requestDate = new DateTime(); // Current date (request submission date)
                    $daysDifference = $requestDate->diff($startDate)->days;

                    if ($daysDifference < 7) {
                        $this->view('demandes/nouvelle', [
                            'error' => 'Les congés payés doivent être demandés au moins 7 jours à l\'avance.',
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'demi_jour_debut' => $demiJourDebut,
                            'demi_jour_fin' => $demiJourFin,
                            'periode_debut' => $periodeDebut,
                            'periode_fin' => $periodeFin,
                            'solde_paye' => $leaveBalances['payé']['remaining'],                            'solde_sans_solde' => $leaveBalances['sans solde']['remaining']
                        ]);
                        return;
                    }
                }

                // VALIDATION RULE 2: For exceptional leave requests, limit to 3 days maximum
                if ($type === 'exceptionnel' && $requestDays > 3) {
                    $this->view('demandes/nouvelle', [
                        'error' => 'Les congés exceptionnels sont limités à 3 jours maximum.',
                        'type' => $type,
                        'date_debut' => $dateDebut,
                        'date_fin' => $dateFin,
                        'motif' => $motif,
                        'demi_jour_debut' => $demiJourDebut,
                        'demi_jour_fin' => $demiJourFin,
                        'periode_debut' => $periodeDebut,
                        'periode_fin' => $periodeFin,
                        'solde_paye' => $leaveBalances['payé']['remaining'],
                        'solde_sans_solde' => $leaveBalances['sans solde']['remaining']
                    ]);
                    return;
                }

                // VALIDATION RULE 3: For exceptional leave requests, check rolling balance
                if ($type === 'exceptionnel') {
                    if (!$this->leaveBalanceModel->hasSufficientExceptionalLeaveBalance($_SESSION['user_id'], $requestDays)) {
                        $exceptionalBalance = $this->leaveBalanceModel->getExceptionalLeaveBalance($_SESSION['user_id']);
                        $currentBalance = $exceptionalBalance['current_balance'];
                        $nextResetTime = date('d/m/Y H:i', strtotime($exceptionalBalance['next_reset_time']));

                        $errorMessage = "Solde de congés exceptionnels insuffisant. Vous avez {$currentBalance} jour(s) disponible(s) mais demandez {$requestDays} jour(s).";
                        $errorMessage .= " Votre solde se remettra à 3 jours le {$nextResetTime}.";

                        $this->view('demandes/nouvelle', [
                            'error' => $errorMessage,
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'demi_jour_debut' => $demiJourDebut,
                            'demi_jour_fin' => $demiJourFin,
                            'periode_debut' => $periodeDebut,
                            'periode_fin' => $periodeFin,
                            'solde_paye' => $leaveBalances['payé']['remaining'],
                            'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                            'exceptional_balance' => $currentBalance,
                            'exceptional_next_reset' => $nextResetTime
                        ]);
                        return;
                    }
                }

                // VALIDATION RULE 4: Check leave balance before allowing submission
                // Use the LeaveBalanceModel for more accurate balance checking

                // Skip balance check for sans solde (unlimited) - exceptional leave is checked above
                if (isset($leaveBalances[$type]) && $type !== 'sans solde') {
                    $remainingDays = $leaveBalances[$type]['remaining'];

                    // Check if user has sufficient balance (skip for sans solde which is unlimited)
                    if (!$this->leaveBalanceModel->hasSufficientBalance($_SESSION['user_id'], $type, $requestDays)) {
                        $this->view('demandes/nouvelle', [
                            'error' => "Solde insuffisant. Vous demandez {$requestDays} jour(s) mais il vous reste seulement {$remainingDays} jour(s) de congé {$this->demandeModel->formatLeaveType($type)}.",
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'demi_jour_debut' => $demiJourDebut,
                            'demi_jour_fin' => $demiJourFin,
                            'periode_debut' => $periodeDebut,
                            'periode_fin' => $periodeFin,
                            // Detailed leave balance information
                            'solde_paye' => $leaveBalances['payé']['remaining'],
                            'solde_paye_used' => $leaveBalances['payé']['used'],
                            'solde_paye_total' => $leaveBalances['payé']['total'],                            'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                            'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
                            'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

                            'solde_maladie' => $leaveBalances['maladie']['remaining'],
                            'solde_maladie_used' => $leaveBalances['maladie']['used'],
                            'solde_maladie_total' => $leaveBalances['maladie']['total'],                            // Accrual information
                            'next_accrual_date' => $nextAccrualDate,
                            'accrual_rate' => $accrualRate
                        ]);
                        return;
                    }
                }

                // VALIDATION RULE 4: Check department leave balance
                $userModel = new UserModel();
                $user = $userModel->getUserById($_SESSION['user_id']);
                $departmentName = $user['departement'];

                // Only check department balance for standard leave types (not special types like exceptional, sick)
                if (!in_array($type, ['exceptionnel', 'maladie'])) {
                    // Get department balance first to show proper error message
                    $departmentBalance = $this->departmentLeaveBalanceModel->getDepartmentLeaveBalance($departmentName);

                    if (!$this->departmentLeaveBalanceModel->hasSufficientDepartmentBalance($departmentName, $requestDays, $type)) {
                        $remainingDays = $departmentBalance['remaining_days'] ?? 0;

                        $this->view('demandes/nouvelle', [
                            'error' => "Solde départemental insuffisant. Votre département ({$departmentName}) dispose de seulement {$remainingDays} jour(s) restants. Les congés exceptionnels ou maladie ne sont pas soumis à cette limite.",
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'demi_jour_debut' => $demiJourDebut,
                            'demi_jour_fin' => $demiJourFin,
                            'periode_debut' => $periodeDebut,
                            'periode_fin' => $periodeFin,
                            // Detailed leave balance information
                            'solde_paye' => $leaveBalances['payé']['remaining'],
                            'solde_paye_used' => $leaveBalances['payé']['used'],
                            'solde_paye_total' => $leaveBalances['payé']['total'],                            'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                            'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
                            'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

                            'solde_maladie' => $leaveBalances['maladie']['remaining'],
                            'solde_maladie_used' => $leaveBalances['maladie']['used'],
                            'solde_maladie_total' => $leaveBalances['maladie']['total'],                            // Accrual information
                            'next_accrual_date' => $nextAccrualDate,
                            'accrual_rate' => $accrualRate
                        ]);
                        return;
                    }
                }

                // Handle file upload if present
                $justificatif = null;
                if (isset($_FILES['justificatif']) && $_FILES['justificatif']['error'] === UPLOAD_ERR_OK) {
                    $uploadDir = 'uploads/justificatifs/';

                    // Create directory if it doesn't exist
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0777, true);
                    }

                    // Generate unique filename
                    $fileExtension = pathinfo($_FILES['justificatif']['name'], PATHINFO_EXTENSION);
                    $fileName = 'justificatif_' . $_SESSION['user_id'] . '_' . time() . '.' . $fileExtension;
                    $targetFile = $uploadDir . $fileName;

                    // Check file size (max 5MB)
                    if ($_FILES['justificatif']['size'] > 5 * 1024 * 1024) {
                        $this->view('demandes/nouvelle', [
                            'error' => 'Le fichier est trop volumineux. La taille maximale est de 5 MB.',
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'demi_jour_debut' => $demiJourDebut,
                            'demi_jour_fin' => $demiJourFin,
                            'periode_debut' => $periodeDebut,
                            'periode_fin' => $periodeFin
                        ]);
                        return;
                    }

                    // Check file type
                    $allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'];
                    if (!in_array($_FILES['justificatif']['type'], $allowedTypes)) {
                        $this->view('demandes/nouvelle', [
                            'error' => 'Type de fichier non autorisé. Les formats acceptés sont: PDF, Word, JPG, PNG.',
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'demi_jour_debut' => $demiJourDebut,
                            'demi_jour_fin' => $demiJourFin,
                            'periode_debut' => $periodeDebut,
                            'periode_fin' => $periodeFin
                        ]);
                        return;
                    }

                    // Move uploaded file
                    if (move_uploaded_file($_FILES['justificatif']['tmp_name'], $targetFile)) {
                        $justificatif = $fileName;
                    } else {
                        $this->view('demandes/nouvelle', [
                            'error' => 'Erreur lors du téléchargement du fichier.',
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'demi_jour_debut' => $demiJourDebut,
                            'demi_jour_fin' => $demiJourFin,
                            'periode_debut' => $periodeDebut,
                            'periode_fin' => $periodeFin
                        ]);
                        return;
                    }
                }

                // Create new demande using the model (now returns validation result)
                $result = $this->demandeModel->createDemande(
                    $_SESSION['user_id'],
                    $type,
                    $dateDebut,
                    $dateFin,
                    $motif,
                    $demiJournee,
                    $demiType,
                    $justificatif
                );

                if ($result['success']) {
                    // Determine the redirect URL based on the request URL
                    $redirectPath = '/mes-demandes';
                    if (strpos($_SERVER['REQUEST_URI'], '/planificateur/nouvelle-demande') === 0) {
                        $redirectPath = '/planificateur/mes-demandes';
                    }
                    elseif (strpos($_SERVER['REQUEST_URI'], '/responsable/nouvelle-demande') === 0) {
                        $redirectPath = '/responsable/mes-demandes';
                    }

                    $successMessage = 'La demande a été créée avec succès.';
                    if (!empty($result['reference'])) {
                        $successMessage .= ' Référence: ' . $result['reference'];
                    }

                    $redirectUrl = $this->buildRedirectUrl($redirectPath, 'success', $successMessage);
                    $this->redirect($redirectUrl);
                } else {
                    // Handle validation errors and conflicts
                    $errorMessage = 'Erreur lors de la création de la demande';
                    $conflicts = [];

                    if (!empty($result['errors'])) {
                        $errorMessage = implode(' ', $result['errors']);
                    }

                    if (!empty($result['conflicts'])) {
                        $conflicts = $result['conflicts'];
                    }

                    // Determine which view to use based on the URL
                    $viewPath = 'demandes/nouvelle';
                    if (strpos($_SERVER['REQUEST_URI'], '/planificateur/nouvelle-demande') === 0) {
                        $viewPath = 'planificateur/nouvelle_demande';
                    }
                    elseif (strpos($_SERVER['REQUEST_URI'], '/responsable/nouvelle-demande') === 0) {
                        $viewPath = 'responsable/nouvelle_demande';
                    }

                    $this->view($viewPath, [
                        'error' => $errorMessage,
                        'conflicts' => $conflicts,
                        'type' => $type,
                        'date_debut' => $dateDebut,
                        'date_fin' => $dateFin,
                        'motif' => $motif,
                        'demi_jour_debut' => $demiJourDebut,
                        'demi_jour_fin' => $demiJourFin,
                        'periode_debut' => $periodeDebut,
                        'periode_fin' => $periodeFin,
                        // Detailed leave balance information
                        'solde_paye' => $leaveBalances['payé']['remaining'],
                        'solde_paye_used' => $leaveBalances['payé']['used'],
                        'solde_paye_total' => $leaveBalances['payé']['total'],                        'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                        'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
                        'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

                        'solde_maladie' => $leaveBalances['maladie']['remaining'],
                        'solde_maladie_used' => $leaveBalances['maladie']['used'],
                        'solde_maladie_total' => $leaveBalances['maladie']['total'],                        // Accrual information
                        'next_accrual_date' => $nextAccrualDate,
                        'accrual_rate' => $accrualRate
                    ]);
                }
            } catch (Exception $e) {
                $this->view('demandes/nouvelle', [
                    'error' => 'Format de date invalide',
                    'type' => $type,
                    'date_debut' => $dateDebut,
                    'date_fin' => $dateFin,
                    'motif' => $motif,
                    // Detailed leave balance information
                    'solde_paye' => $leaveBalances['payé']['remaining'],
                    'solde_paye_used' => $leaveBalances['payé']['used'],
                    'solde_paye_total' => $leaveBalances['payé']['total'],

                    'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                    'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
                    'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

                    'solde_maladie' => $leaveBalances['maladie']['remaining'],
                    'solde_maladie_used' => $leaveBalances['maladie']['used'],
                    'solde_maladie_total' => $leaveBalances['maladie']['total'],

                    // Accrual information
                    'next_accrual_date' => $nextAccrualDate,
                    'accrual_rate' => $accrualRate
                ]);
            }
        } else {
            // Determine which view to use based on the URL
            $viewPath = 'demandes/nouvelle';

            // Check if the request is coming from the planificateur route
            if (strpos($_SERVER['REQUEST_URI'], '/planificateur/nouvelle-demande') === 0) {
                $viewPath = 'planificateur/nouvelle_demande';
            }
            // Check if the request is coming from the responsable route
            elseif (strpos($_SERVER['REQUEST_URI'], '/responsable/nouvelle-demande') === 0) {
                $viewPath = 'responsable/nouvelle_demande';
            }

            // Display form with leave balances
            $this->view($viewPath, [
                // Detailed leave balance information
                'solde_paye' => $leaveBalances['payé']['remaining'],
                'solde_paye_used' => $leaveBalances['payé']['used'],
                'solde_paye_total' => $leaveBalances['payé']['total'],

                'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
                'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

                'solde_maladie' => $leaveBalances['maladie']['remaining'],
                'solde_maladie_used' => $leaveBalances['maladie']['used'],
                'solde_maladie_total' => $leaveBalances['maladie']['total'],

                // Accrual information
                'next_accrual_date' => $nextAccrualDate,
                'accrual_rate' => $accrualRate,

                // Exceptional leave balance data (rolling 3-day system)
                'exceptional_balance' => $leaveBalances['exceptionnel']['remaining'],
                'exceptional_next_reset' => isset($leaveBalances['exceptionnel']['next_reset_time']) ?
                    date('d/m/Y H:i', strtotime($leaveBalances['exceptionnel']['next_reset_time'])) : null,
                'can_request_exceptional' => $leaveBalances['exceptionnel']['can_request']
            ]);
        }
    }

    // Display form to edit an existing demande
    public function modifier() {
        $id = $_GET['id'] ?? 0;

        if (empty($id)) {
            $this->redirect('/mes-demandes');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Process form submission
            $type = $_POST['type'] ?? '';
            $dateDebut = $_POST['date_debut'] ?? '';
            $dateFin = $_POST['date_fin'] ?? '';
            $motif = $_POST['motif'] ?? '';

            // Handle half-day options
            $demiJourDebut = isset($_POST['demi_jour_debut']) ? 1 : 0;
            $demiJourFin = isset($_POST['demi_jour_fin']) ? 1 : 0;
            $periodeDebut = $_POST['periode_debut'] ?? 'matin';
            $periodeFin = $_POST['periode_fin'] ?? 'apres-midi';

            // Determine demi_type value
            $demiType = null;
            if ($demiJourDebut && $demiJourFin) {
                $demiType = $periodeDebut . ',' . $periodeFin;
            } elseif ($demiJourDebut) {
                $demiType = $periodeDebut;
            } elseif ($demiJourFin) {
                $demiType = $periodeFin;
            }

            // Set demi_journee flag
            $demiJournee = ($demiJourDebut || $demiJourFin) ? 1 : 0;

            // Trim whitespace from input values
            $type = trim($type);
            $dateDebut = trim($dateDebut);
            $dateFin = trim($dateFin);
            $motif = trim($motif);

            // Validate input - check for empty or whitespace-only values
            $missingFields = [];
            if (empty($type)) {
                $missingFields[] = 'Type de demande';
            }
            if (empty($dateDebut)) {
                $missingFields[] = 'Date de début';
            }
            if (empty($dateFin)) {
                $missingFields[] = 'Date de fin';
            }

            if (!empty($missingFields)) {
                // Add debugging information to help identify the issue
                error_log("Modifier validation failed - Missing fields: " . implode(', ', $missingFields) . " - Type: '$type', Date debut: '$dateDebut', Date fin: '$dateFin'");

                $errorMessage = 'Les champs suivants sont obligatoires : ' . implode(', ', $missingFields);

                $this->view('demandes/modifier', [
                    'error' => $errorMessage,
                    'demande' => [
                        'id' => $id,
                        'type' => $type,
                        'date_debut' => $dateDebut,
                        'date_fin' => $dateFin,
                        'motif' => $motif,
                        'statut' => 'en cours',
                        'demi_journee' => $demiJournee,
                        'demi_type' => $demiType
                    ]
                ]);
                return;
            }

            try {
                // Validate dates
                $startDate = new DateTime($dateDebut);
                $endDate = new DateTime($dateFin);

                if ($endDate < $startDate) {
                    $this->view('demandes/modifier', [
                        'error' => 'La date de fin doit être postérieure ou égale à la date de début',
                        'demande' => [
                            'id' => $id,
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'statut' => 'en cours'
                        ]
                    ]);
                    return;
                }

                // Validate future dates for new leave requests
                $today = new DateTime();
                $today->setTime(0, 0, 0); // Reset time to start of day for fair comparison

                if ($startDate < $today) {
                    $this->view('demandes/modifier', [
                        'error' => 'La date de début ne peut pas être dans le passé',
                        'demande' => [
                            'id' => $id,
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'statut' => 'en cours'
                        ]
                    ]);
                    return;
                }

                // Check if the demande exists and can be modified
                $existingDemande = $this->demandeModel->getDemandeById($id, $_SESSION['user_id']);
                if (!$existingDemande) {
                    $this->redirect('/mes-demandes');
                    return;
                }

                // Only "en cours" demandes can be modified
                if (strtolower($existingDemande['statut']) !== 'en cours') {
                    $this->view('demandes/modifier', [
                        'error' => 'Cette demande ne peut plus être modifiée',
                        'demande' => $existingDemande
                    ]);
                    return;
                }

                // Handle file upload if present
                $justificatif = null;
                if (isset($_FILES['justificatif']) && $_FILES['justificatif']['error'] === UPLOAD_ERR_OK) {
                    $uploadDir = 'uploads/justificatifs/';

                    // Create directory if it doesn't exist
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0777, true);
                    }

                    // Generate unique filename
                    $fileExtension = pathinfo($_FILES['justificatif']['name'], PATHINFO_EXTENSION);
                    $fileName = 'justificatif_' . $_SESSION['user_id'] . '_' . time() . '.' . $fileExtension;
                    $targetFile = $uploadDir . $fileName;

                    // Check file size (max 5MB)
                    if ($_FILES['justificatif']['size'] > 5 * 1024 * 1024) {
                        $this->view('demandes/modifier', [
                            'error' => 'Le fichier est trop volumineux. La taille maximale est de 5 MB.',
                            'demande' => [
                                'id' => $id,
                                'type' => $type,
                                'date_debut' => $dateDebut,
                                'date_fin' => $dateFin,
                                'motif' => $motif,
                                'statut' => 'en cours',
                                'demi_journee' => $demiJournee,
                                'demi_type' => $demiType
                            ]
                        ]);
                        return;
                    }

                    // Check file type
                    $allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'];
                    if (!in_array($_FILES['justificatif']['type'], $allowedTypes)) {
                        $this->view('demandes/modifier', [
                            'error' => 'Type de fichier non autorisé. Les formats acceptés sont: PDF, Word, JPG, PNG.',
                            'demande' => [
                                'id' => $id,
                                'type' => $type,
                                'date_debut' => $dateDebut,
                                'date_fin' => $dateFin,
                                'motif' => $motif,
                                'statut' => 'en cours',
                                'demi_journee' => $demiJournee,
                                'demi_type' => $demiType
                            ]
                        ]);
                        return;
                    }

                    // Move uploaded file
                    if (move_uploaded_file($_FILES['justificatif']['tmp_name'], $targetFile)) {
                        $justificatif = $fileName;
                    } else {
                        $this->view('demandes/modifier', [
                            'error' => 'Erreur lors du téléchargement du fichier.',
                            'demande' => [
                                'id' => $id,
                                'type' => $type,
                                'date_debut' => $dateDebut,
                                'date_fin' => $dateFin,
                                'motif' => $motif,
                                'statut' => 'en cours',
                                'demi_journee' => $demiJournee,
                                'demi_type' => $demiType
                            ]
                        ]);
                        return;
                    }
                }

                // Update demande using the model
                $success = $this->demandeModel->updateDemande(
                    $id,
                    $_SESSION['user_id'],
                    $type,
                    $dateDebut,
                    $dateFin,
                    $motif,
                    $demiJournee,
                    $demiType,
                    $justificatif
                );

                if ($success) {
                    $redirectUrl = $this->buildRedirectUrl('/mes-demandes', 'success', 'La demande a été modifiée avec succès.');
                    $this->redirect($redirectUrl);
                } else {
                    $this->view('demandes/modifier', [
                        'error' => 'Erreur lors de la modification de la demande',
                        'demande' => [
                            'id' => $id,
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'statut' => 'en cours',
                            'demi_journee' => $demiJournee,
                            'demi_type' => $demiType
                        ]
                    ]);
                }
            } catch (Exception $e) {
                $this->view('demandes/modifier', [
                    'error' => 'Format de date invalide',
                    'demande' => [
                        'id' => $id,
                        'type' => $type,
                        'date_debut' => $dateDebut,
                        'date_fin' => $dateFin,
                        'motif' => $motif,
                        'statut' => 'en cours'
                    ]
                ]);
            }
        } else {
            // Get demande details from database
            $demande = $this->demandeModel->getDemandeById($id, $_SESSION['user_id']);

            if (!$demande) {
                $this->redirect('/mes-demandes');
                return;
            }

            // Display form with demande details
            $this->view('demandes/modifier', ['demande' => $demande]);
        }
    }

    // Cancel a demande
    public function annuler() {
        // Ensure the request has a valid ID
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

        if ($id <= 0) {
            $this->redirect($this->getRedirectUrl());
            return;
        }

        try {
            // First check if the demande exists and belongs to the current user
            $demande = $this->demandeModel->getDemandeById($id, $_SESSION['user_id']);

            if (!$demande) {
                // If demande doesn't exist or doesn't belong to user
                $this->redirect($this->getRedirectUrl());
                return;
            }

            // Check if the demande can be cancelled (allow both 'en cours' and 'acceptee' statuses)
            if (strtolower($demande['statut']) !== 'en cours' && strtolower($demande['statut']) !== 'acceptee') {
                // Preserve current filters and pagination
                $redirectUrl = $this->buildRedirectUrl($this->getRedirectUrl(), 'error', 'Vous ne pouvez pas annuler une demande qui a été refusée ou déjà annulée.');
                $this->redirect($redirectUrl);
                return;
            }

            // Cancel demande using the database model
            $success = $this->demandeModel->cancelDemande($id, $_SESSION['user_id']);

            // Redirect with appropriate message
            if ($success) {
                $redirectUrl = $this->buildRedirectUrl($this->getRedirectUrl(), 'success', 'La demande a été annulée avec succès.');
                $this->redirect($redirectUrl);
            } else {
                $redirectUrl = $this->buildRedirectUrl($this->getRedirectUrl(), 'error', 'Une erreur est survenue lors de l\'annulation de la demande.');
                $this->redirect($redirectUrl);
            }
        } catch (Exception $e) {
            // Handle any unexpected exceptions
            $redirectUrl = $this->buildRedirectUrl($this->getRedirectUrl(), 'error', 'Une erreur système est survenue. Veuillez réessayer plus tard.');
            $this->redirect($redirectUrl);
        }
    }

    // Role-specific cancel demande method for routes like /responsable/annuler-demande
    public function annulerDemande() {
        // Just call the regular annuler method
        $this->annuler();
    }

    /**
     * Display details of a demande
     */
    public function details() {
        // Ensure the request has a valid ID
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

        if ($id <= 0) {
            $this->redirect($this->getRedirectUrl());
            return;
        }

        try {
            // Get demande details from database based on user role
            $demande = null;

            if ($_SESSION['role'] === 'admin') {
                // Admin can see all demandes
                $demande = $this->demandeModel->getDemandeById($id);
            } elseif ($_SESSION['role'] === 'responsable') {
                // First check if this is the responsable's own demande
                $ownDemande = $this->demandeModel->getDemandeById($id, $_SESSION['user_id']);

                if ($ownDemande) {
                    // If it's the responsable's own demande, use that
                    $demande = $ownDemande;
                } else {
                    // Otherwise, check if it's a demande from one of their team members
                    $demande = $this->demandeModel->getDemandeByIdForResponsable($id, $_SESSION['user_id']);
                }
            } elseif ($_SESSION['role'] === 'planificateur') {
                // First check if this is the planificateur's own demande
                $ownDemande = $this->demandeModel->getDemandeById($id, $_SESSION['user_id']);

                if ($ownDemande) {
                    // If it's the planificateur's own demande, use that
                    $demande = $ownDemande;
                } else {
                    // Otherwise, planificateur can see all demandes
                    $demande = $this->demandeModel->getDemandeById($id);
                }
            } else {
                // Employe can only see their own demandes
                $demande = $this->demandeModel->getDemandeById($id, $_SESSION['user_id']);
            }

            if (!$demande) {
                // If demande doesn't exist or user doesn't have permission to view it
                $this->redirect($this->getRedirectUrl());
                return;
            }

            // Determine which view to use based on the URL and role
            $viewPath = 'demandes/details';

            // Check if the request is coming from a specific planificateur route
            if (strpos($_SERVER['REQUEST_URI'], '/planificateur/details-demande') === 0) {
                $viewPath = 'planificateur/details_demande';
            }
            // Check if the request is coming from a specific responsable route
            elseif (strpos($_SERVER['REQUEST_URI'], '/responsable/details-demande') === 0) {
                $viewPath = 'responsable/details_demande';
            }

            // Display details view
            $this->view($viewPath, ['demande' => $demande]);
        } catch (Exception $e) {
            // Handle any unexpected exceptions
            $redirectUrl = $this->buildRedirectUrl($this->getRedirectUrl(), 'error', 'Une erreur système est survenue. Veuillez réessayer plus tard.');
            $this->redirect($redirectUrl);
        }
    }

    /**
     * Get the appropriate redirect URL based on user role
     */
    private function getRedirectUrl() {
        // Check if the request is coming from a specific planificateur route
        if (strpos($_SERVER['REQUEST_URI'], '/planificateur/mes-demandes') === 0 ||
            strpos($_SERVER['REQUEST_URI'], '/planificateur/details-demande') === 0 ||
            strpos($_SERVER['REQUEST_URI'], '/planificateur/annuler-demande') === 0) {
            return '/planificateur/mes-demandes';
        }
        // Check if the request is coming from a specific responsable route
        if (strpos($_SERVER['REQUEST_URI'], '/responsable/mes-demandes') === 0 ||
            strpos($_SERVER['REQUEST_URI'], '/responsable/details-demande') === 0 ||
            strpos($_SERVER['REQUEST_URI'], '/responsable/annuler-demande') === 0) {
            return '/responsable/mes-demandes';
        }

        switch ($_SESSION['role']) {
            case 'admin':
                return '/all-demandes';
            case 'responsable':
                // Check if we're in the context of the responsable's own requests
                if (strpos($_SERVER['REQUEST_URI'], '/responsable/mes-demandes') === 0 ||
                    (strpos($_SERVER['REQUEST_URI'], '/details-demande') === 0 && isset($_GET['id']))) {

                    // If we're coming from details page, check if it's the responsable's own demande
                    if (strpos($_SERVER['REQUEST_URI'], '/details-demande') === 0 && isset($_GET['id'])) {
                        $demandeId = (int)$_GET['id'];
                        $ownDemande = $this->demandeModel->getDemandeById($demandeId, $_SESSION['user_id']);

                        if ($ownDemande) {
                            return '/responsable/mes-demandes';
                        } else {
                            return '/responsable/demandes_approbation';
                        }
                    }

                    return '/responsable/mes-demandes';
                }
                return '/responsable/demandes_approbation';
            case 'planificateur':
                // Check if we're in the context of the planificateur's own requests
                if (strpos($_SERVER['REQUEST_URI'], '/planificateur/mes-demandes') === 0 ||
                    (strpos($_SERVER['REQUEST_URI'], '/details-demande') === 0 && isset($_GET['id']))) {

                    // If we're coming from details page, check if it's the planificateur's own demande
                    if (strpos($_SERVER['REQUEST_URI'], '/details-demande') === 0 && isset($_GET['id'])) {
                        $demandeId = (int)$_GET['id'];
                        $ownDemande = $this->demandeModel->getDemandeById($demandeId, $_SESSION['user_id']);

                        if ($ownDemande) {
                            return '/planificateur/mes-demandes';
                        } else {
                            return '/planificateur/demandes_approbation';
                        }
                    }

                    return '/planificateur/mes-demandes';
                }
                return '/absences-a-venir';
            case 'employe':
            default:
                return '/mes-demandes';
        }
    }

    /**
     * Helper method to build a redirect URL with preserved filters and a message
     *
     * @param string $baseUrl The base URL to redirect to
     * @param string $messageType The type of message (success or error)
     * @param string $message The message to display
     * @return string The complete redirect URL
     */
    private function buildRedirectUrl($baseUrl, $messageType, $message) {
        // Get current filters from URL
        $params = [];

        // Preserve current filters
        $filterParams = ['period', 'status', 'type', 'search', 'page', 'per_page', 'sort_by', 'sort_order'];
        foreach ($filterParams as $param) {
            if (isset($_GET[$param])) {
                $params[$param] = $_GET[$param];
            }
        }

        // Add message
        $params[$messageType] = $message;

        // Build URL
        $queryString = http_build_query($params);
        return $baseUrl . ($queryString ? '?' . $queryString : '');
    }

    // API endpoint for validating leave requests (AJAX)
    public function validateRequest() {
        // Set JSON response header
        header('Content-Type: application/json');

        // Only allow POST requests
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            return;
        }

        try {
            // Get JSON input
            $input = json_decode(file_get_contents('php://input'), true);

            if (!$input) {
                // Fallback to POST data
                $input = $_POST;
            }

            $userId = $_SESSION['user_id'];
            $startDate = trim($input['date_debut'] ?? '');
            $endDate = trim($input['date_fin'] ?? '');
            $type = trim($input['type'] ?? '');
            $demiJournee = isset($input['demi_journee']) ? (bool)$input['demi_journee'] : false;
            $demiType = $input['demi_type'] ?? null;

            // Basic validation - check for empty or whitespace-only values
            $missingFields = [];
            if (empty($type)) {
                $missingFields[] = 'Type de demande';
            }
            if (empty($startDate)) {
                $missingFields[] = 'Date de début';
            }
            if (empty($endDate)) {
                $missingFields[] = 'Date de fin';
            }

            if (!empty($missingFields)) {
                // Add debugging information to help identify the issue
                error_log("AJAX validation failed - Missing fields: " . implode(', ', $missingFields) . " - Type: '$type', Start date: '$startDate', End date: '$endDate'");

                $errorMessage = 'Les champs suivants sont obligatoires : ' . implode(', ', $missingFields);

                echo json_encode([
                    'valid' => false,
                    'errors' => [$errorMessage],
                    'conflicts' => []
                ]);
                return;
            }

            // Validate date format
            if (!$this->isValidDate($startDate) || !$this->isValidDate($endDate)) {
                echo json_encode([
                    'valid' => false,
                    'errors' => ['Format de date invalide.'],
                    'conflicts' => []
                ]);
                return;
            }

            // Validate date logic
            $start = new DateTime($startDate);
            $end = new DateTime($endDate);
            $today = new DateTime();
            $today->setTime(0, 0, 0);

            if ($end < $start) {
                echo json_encode([
                    'valid' => false,
                    'errors' => ['La date de fin doit être postérieure ou égale à la date de début.'],
                    'conflicts' => []
                ]);
                return;
            }

            if ($start < $today) {
                echo json_encode([
                    'valid' => false,
                    'errors' => ['La date de début ne peut pas être dans le passé.'],
                    'conflicts' => []
                ]);
                return;
            }

            // Perform overlap validation
            $validation = $this->demandeModel->validateLeaveRequest(
                $userId,
                $startDate,
                $endDate,
                $type,
                $demiJournee,
                $demiType
            );

            echo json_encode($validation);

        } catch (Exception $e) {
            error_log('Error in validateRequest: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'valid' => false,
                'errors' => ['Erreur interne du serveur.'],
                'conflicts' => []
            ]);
        }
    }

    // Helper method to validate date format
    private function isValidDate($date) {
        if (empty($date)) {
            return false;
        }

        // Try to validate the date format
        $format = 'Y-m-d'; // ISO format (YYYY-MM-DD)

        $parsedDate = DateTime::createFromFormat($format, $date);

        // Check if the date was successfully parsed and if it matches the input
        return $parsedDate && $parsedDate->format($format) === $date;
    }

    // Handle document request AJAX calls
    public function demande_document() {
        // Only handle POST requests
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            return;
        }

        // Check if it's an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid request']);
            return;
        }

        try {
            // Get JSON input
            $input = json_decode(file_get_contents('php://input'), true);

            if (!$input) {
                throw new Exception('Invalid JSON data');
            }

            $type = $input['type'] ?? '';
            $documentName = $input['document_name'] ?? '';

            // Validate input
            if (empty($type) || empty($documentName)) {
                throw new Exception('Type et nom du document sont requis');
            }

            // Validate document type
            $allowedTypes = ['attestation_travail', 'fiche_paie', 'attestation_salaire'];
            if (!in_array($type, $allowedTypes)) {
                throw new Exception('Type de document non autorisé');
            }

            // Load DocumentRequestModel
            $documentRequestModel = new DocumentRequestModel();

            // Create the document request
            $success = $documentRequestModel->createDocumentRequest(
                $_SESSION['user_id'],
                $type,
                $documentName,
                null // No comments for now
            );

            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Demande de document créée avec succès'
                ]);
            } else {
                throw new Exception('Erreur lors de la création de la demande');
            }

        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}
